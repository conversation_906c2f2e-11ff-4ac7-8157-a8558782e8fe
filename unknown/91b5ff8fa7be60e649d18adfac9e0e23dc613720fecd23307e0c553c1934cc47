%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!30 &1
GraphicsSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 16
  m_Deferred:
    m_Mode: 1
    m_Shader: {fileID: 69, guid: 0000000000000000f000000000000000, type: 0}
  m_DeferredReflections:
    m_Mode: 1
    m_Shader: {fileID: 74, guid: 0000000000000000f000000000000000, type: 0}
  m_ScreenSpaceShadows:
    m_Mode: 1
    m_Shader: {fileID: 64, guid: 0000000000000000f000000000000000, type: 0}
  m_DepthNormals:
    m_Mode: 1
    m_Shader: {fileID: 62, guid: 0000000000000000f000000000000000, type: 0}
  m_MotionVectors:
    m_Mode: 1
    m_Shader: {fileID: 75, guid: 0000000000000000f000000000000000, type: 0}
  m_LightHalo:
    m_Mode: 1
    m_Shader: {fileID: 105, guid: 0000000000000000f000000000000000, type: 0}
  m_LensFlare:
    m_Mode: 1
    m_Shader: {fileID: 102, guid: 0000000000000000f000000000000000, type: 0}
  m_VideoShadersIncludeMode: 2
  m_AlwaysIncludedShaders:
  - {fileID: 7, guid: 0000000000000000f000000000000000, type: 0}
  - {fileID: 15104, guid: 0000000000000000f000000000000000, type: 0}
  - {fileID: 15105, guid: 0000000000000000f000000000000000, type: 0}
  - {fileID: 15106, guid: 0000000000000000f000000000000000, type: 0}
  - {fileID: 10753, guid: 0000000000000000f000000000000000, type: 0}
  - {fileID: 10770, guid: 0000000000000000f000000000000000, type: 0}
  - {fileID: 16000, guid: 0000000000000000f000000000000000, type: 0}
  - {fileID: 17000, guid: 0000000000000000f000000000000000, type: 0}
  - {fileID: 16001, guid: 0000000000000000f000000000000000, type: 0}
  m_PreloadedShaders: []
  m_PreloadShadersBatchTimeLimit: -1
  m_SpritesDefaultMaterial: {fileID: 10754, guid: 0000000000000000f000000000000000,
    type: 0}
  m_CustomRenderPipeline: {fileID: 11400000, guid: 6cbb334173594044d902c240207198f2,
    type: 2}
  m_TransparencySortMode: 0
  m_TransparencySortAxis: {x: 0, y: 0, z: 1}
  m_DefaultRenderingPath: 1
  m_DefaultMobileRenderingPath: 1
  m_TierSettings:
  - serializedVersion: 5
    m_BuildTarget: 13
    m_Tier: 0
    m_Settings:
      standardShaderQuality: 0
      renderingPath: 1
      hdrMode: 1
      realtimeGICPUUsage: 25
      useReflectionProbeBoxProjection: 1
      useReflectionProbeBlending: 1
      useHDR: 1
      useDetailNormalMap: 1
      useCascadedShadowMaps: 1
      prefer32BitShadowMaps: 0
      enableLPPV: 1
      useDitherMaskForAlphaBlendedShadows: 1
    m_Automatic: 0
  - serializedVersion: 5
    m_BuildTarget: 13
    m_Tier: 1
    m_Settings:
      standardShaderQuality: 1
      renderingPath: 1
      hdrMode: 1
      realtimeGICPUUsage: 25
      useReflectionProbeBoxProjection: 1
      useReflectionProbeBlending: 1
      useHDR: 1
      useDetailNormalMap: 1
      useCascadedShadowMaps: 1
      prefer32BitShadowMaps: 0
      enableLPPV: 1
      useDitherMaskForAlphaBlendedShadows: 1
    m_Automatic: 0
  - serializedVersion: 5
    m_BuildTarget: 13
    m_Tier: 2
    m_Settings:
      standardShaderQuality: 2
      renderingPath: 1
      hdrMode: 1
      realtimeGICPUUsage: 25
      useReflectionProbeBoxProjection: 1
      useReflectionProbeBlending: 1
      useHDR: 1
      useDetailNormalMap: 1
      useCascadedShadowMaps: 1
      prefer32BitShadowMaps: 0
      enableLPPV: 1
      useDitherMaskForAlphaBlendedShadows: 1
    m_Automatic: 0
  m_LightmapStripping: 0
  m_FogStripping: 0
  m_InstancingStripping: 0
  m_BrgStripping: 0
  m_LightmapKeepPlain: 0
  m_LightmapKeepDirCombined: 0
  m_LightmapKeepDynamicPlain: 0
  m_LightmapKeepDynamicDirCombined: 0
  m_LightmapKeepShadowMask: 0
  m_LightmapKeepSubtractive: 0
  m_FogKeepLinear: 0
  m_FogKeepExp: 0
  m_FogKeepExp2: 0
  m_AlbedoSwatchInfos: []
  m_RenderPipelineGlobalSettingsMap:
    UnityEngine.Rendering.Universal.UniversalRenderPipeline: {fileID: 11400000, guid: 76c98309ae61cd243932c14e893ccd2f,
      type: 2}
  m_LightsUseLinearIntensity: 1
  m_LightsUseColorTemperature: 1
  m_LogWhenShaderIsCompiled: 0
  m_LightProbeOutsideHullStrategy: 0
  m_CameraRelativeLightCulling: 0
  m_CameraRelativeShadowCulling: 0
