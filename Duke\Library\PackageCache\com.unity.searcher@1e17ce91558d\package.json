{"name": "com.unity.searcher", "displayName": "Searcher", "version": "4.9.3", "unity": "2019.1", "description": "General search window for use in the Editor. First target use is for GraphView node search.", "keywords": ["search", "searcher"], "_upm": {"changelog": "- Fixed a bug where spaces are removed when highlighted.\n- Changed VisualSplitter to twoPaneSplitView and updated indentation"}, "upmCi": {"footprint": "5b1ed6d260daf7b5a1f6f503b6d4e4e90d584762"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.searcher@4.9/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/com.unity.searcher.git", "type": "git", "revision": "6fad693b6604ae7175b59ebb4990d9a0b6c1d012"}, "samples": [{"displayName": "Searchers Samples", "description": "Some Searcher's basic examples", "path": "Samples~"}], "_fingerprint": "1e17ce91558d1d9127554adc03d275f39a7466a2"}