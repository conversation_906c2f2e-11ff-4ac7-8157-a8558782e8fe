# Testing Lost Crypt

Welcome to the this training material for the Unity Test Framework (UTF).  
  
The training is structured with a selection of exercises, starting with more basic topics and then expanding on that knowledge.  
  
Each section has a Learning Objectives section, which can help you pick what exercises will teach you new things. The exercises are grouped thematically and their difficulty varies.  
  
**This course focus on testing an actual game. Our candidate is the [LostCrypt](https://assetstore.unity.com/packages/essentials/tutorial-projects/lost-crypt-2d-sample-project-158673) example project.**  

## Course outline

0. [Setting up](./setting-up.md)
1. [Running a test in LostCrypt](./first-test.md)
2. [Moving character](./moving-character.md)
3. [Reach wand test](./reach-wand-test.md)
4. [Collision test](./collision-test.md)
5. [Asset change test](./asset-change-test.md)
6. [Scene validation test](./scene-validation-test.md)
7. [Performance tests](./performance-tests.md)

## Prerequisites

Although not technically a requirement, we strongly recommend you complete the [General Introduction](../welcome.md) course before attempting this one.
